import router from "./router/";
import store from "./store";
import { getToken } from "@/utils/auth";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { ElLoading, ElMessageBox } from "element-plus";
import { baseUrl } from "@/config/env";
import website from "@/config/website";
NProgress.configure({ showSpinner: false });

const casLoginFn = async (to) => {
  if (!to.query.cas && globalThis.__CAS_LOGIN) {
    const loading = ElLoading.service({
      lock: true,
      text: "登录中,请稍后",
      background: "rgba(0, 0, 0, 0.7)",
    });
    try {
      await store.dispatch("LoginByCas");
      await store.dispatch("FlowRoutes");
      return store.getters.tagWel || "/";
    } catch (_error) {
      console.error("Login failed:", _error);
      ElMessageBox.confirm("登录失败，请重新登录", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        window.location.href = `${baseUrl}${website.casLoginUrl}`;
      });
      return false;
    } finally {
      loading.close();
    }
  } else {
    return false;
  }
};
const lockPage = "/lock"; //锁屏页
router.beforeEach((to, from, next) => {
  // 获取匹配的路由数组
  const matchedRoutes = to.matched;
  const component = matchedRoutes.length > 0 ? matchedRoutes[matchedRoutes.length - 1].components.default : null;
  if (component && typeof component == "function") {
    component().then((mod) => {
      mod.default.name = to.fullPath;
    });
  }
  const meta = to.meta || {};
  const isMenu = meta.menu === undefined ? to.query.menu : meta.menu;
  store.commit("SET_IS_MENU", isMenu === undefined);

  if (getToken()) {
    if (store.getters.isLock && to.path !== lockPage) {
      //如果系统激活锁屏，全部跳转到锁屏页
      next({ path: lockPage });
    } else if (to.meta.permission && !store.getters.permission[to.meta.permission]) {
      next({ path: "/403" });
    } else if (to.path === "/login") {
      //如果登录成功访问登录页跳转到主页
      next({ path: "/" });
    } else if (Array.isArray(store.getters.menu) && store.getters.menu.length === 0) {
      const getMenu = async () => {
        const data = await store.dispatch("GetMenu");
        if (data.length !== 0) {
          router.$avueRouter.formatRoutes(data, true);
          next(to);
        }
      };
      getMenu();
    } else {
      if (store.getters.token.length === 0) {
        const handleLogout = async () => {
          try {
            await store.dispatch("FedLogOut");
            const redirectPath = await casLoginFn(to);
            next(redirectPath);
          } catch (error) {
            console.error("Logout and login failed:", error);
            next("/login");
          }
        };
        handleLogout();
      } else {
        const meta = to.meta || {};
        const query = to.query || {};
        if (meta.target) {
          window.open(query.url.replace(/#/g, "&"));
          return;
        } else if (meta.isTab !== false && meta.isNewTab !== 1) {
          store.commit("ADD_TAG", {
            name: query.name || to.name,
            path: to.path,
            fullPath: to.path,
            params: to.params,
            query: to.query,
            meta: meta,
          });
        }
        next();
      }
    }
  } else {
    //判断是否需要认证，没有登录访问去登录页
    if ((meta.isAuth === false && to.path !== "/login") || (meta.isAuth === false && (!globalThis.__CAS_LOGIN || to.query.cas) && to.path === "/login")) {
      next();
    } else {
      const handleCasLogin = async () => {
        try {
          const redirectPath = await casLoginFn(to);
          next(redirectPath);
        } catch (error) {
          console.error("CAS login failed:", error);
          next("/login");
        }
      };
      handleCasLogin();
    }
  }
});

router.afterEach((to) => {
  NProgress.done();
  let title = router.$avueRouter.generateTitle(to, { label: "name" });
  router.$avueRouter.setTitle(title);
  store.commit("SET_IS_SEARCH", false);
});
