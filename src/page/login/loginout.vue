<template>
  <div class="logout-container">
    <!-- 时间显示 -->
    <div class="logout-time">
      {{ time }}
    </div>

    <div class="logout-wrapper">
      <!-- 左侧品牌展示区域 -->
      <div class="logout-left animate__animated animate__fadeInLeft">
        <img class="img" src="/img/logo.png" alt="系统Logo" />
        <p class="title">{{ $t("logout.info") }}</p>
      </div>

      <!-- 右侧退出确认区域 -->
      <div class="logout-border animate__animated animate__fadeInRight">
        <div class="logout-main">
          <!-- 退出状态图标 -->
          <div class="logout-icon animate__animated animate__bounceIn">
            <el-icon :size="80" color="#2C77F1">
              <CircleCheck />
            </el-icon>
          </div>

          <!-- 退出状态提示 -->
          <h2 class="logout-title">{{ $t("logout.title") }}</h2>
          <p class="logout-message">{{ $t("logout.message") }}</p>

          <!-- 用户信息显示 -->
          <div class="user-info" v-if="userInfo">
            <p class="user-name">{{ userInfo.user_name || userInfo.account }}</p>
            <p class="logout-time-info">{{ $t("logout.logoutTime") }}: {{ logoutTime }}</p>
          </div>

          <!-- 操作按钮区域 -->
          <div class="logout-actions">
            <el-button type="primary" size="large" class="relogin-btn" @click="handleReLogin" :loading="loading">
              <el-icon class="mr-2"><User /></el-icon>
              {{ $t("logout.reLogin") }}
            </el-button>

            <el-button size="large" class="home-btn" @click="handleGoHome">
              <el-icon class="mr-2"><House /></el-icon>
              {{ $t("logout.goHome") }}
            </el-button>
          </div>

          <!-- 安全提示 -->
          <div class="security-tip">
            <el-icon color="#909399"><InfoFilled /></el-icon>
            <span>{{ $t("logout.securityTip") }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { CircleCheck, User, House, InfoFilled } from "@element-plus/icons-vue";
import { getStore } from "@/utils/store";
import dayjs from "dayjs";

// 定义组件选项
defineOptions({
  name: "LoginLoginout",
});

// 组合式API
const router = useRouter();
const store = useStore();
const { t } = useI18n();

// 响应式数据
const time = ref("");
const logoutTime = ref("");
const loading = ref(false);
const userInfo = ref(null);
let timeInterval = null;

// 生命周期钩子
onMounted(() => {
  initPage();
  startTimeUpdate();
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

// 方法定义
const initPage = () => {
  // 获取用户信息（如果还存在的话）
  userInfo.value = getStore({ name: "userInfo" }) || null;

  // 设置退出时间
  logoutTime.value = dayjs().format("YYYY年MM月DD日 HH:mm:ss");

  // 确保用户已经退出登录
  ensureLoggedOut();
};

const startTimeUpdate = () => {
  // 更新当前时间显示
  const updateTime = () => {
    time.value = dayjs().format("YYYY年MM月DD日 HH:mm:ss");
  };

  updateTime();
  timeInterval = setInterval(updateTime, 1000);
};

const ensureLoggedOut = () => {
  // 确保用户状态已清除
  if (store.getters.token) {
    store.dispatch("FedLogOut").catch((err) => {
      console.warn("清除用户状态失败:", err);
    });
  }
};

const handleReLogin = async () => {
  loading.value = true;

  try {
    // 添加一个短暂的延迟，提升用户体验
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 跳转到登录页面
    await router.push("/login");

    ElMessage.success(t("logout.redirectToLogin"));
  } catch (error) {
    console.error("跳转登录页面失败:", error);
    ElMessage.error(t("logout.redirectError"));
  } finally {
    loading.value = false;
  }
};

const handleGoHome = () => {
  // 跳转到首页或者其他公开页面
  window.location.href = "/";
};
</script>

<style lang="scss" scoped>
// 导入项目变量
@use "@/styles/variables.scss" as *;

.logout-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100vh;
  background: linear-gradient(110deg, rgb(2, 40, 85) 0%, rgb(44, 119, 241) 50%, rgb(255, 255, 255) 50%, rgb(240, 240, 240) 100%);
  overflow: hidden;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin-left: -48%;
    background-image: url(/img/login-bg.svg);
    background-position: 100%;
    background-repeat: no-repeat;
    background-size: auto 100%;
    content: "";
  }
}

.logout-time {
  position: absolute;
  left: 25px;
  top: 25px;
  color: #fff;
  font-weight: bold;
  opacity: 0.9;
  font-size: 18px;
  z-index: 10;
}

.logout-wrapper {
  margin: 0 auto;
  width: 100%;
  display: flex;
  min-height: 500px;
}

.logout-left {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50%;
  padding-top: 100px;
  color: #fff;
  box-sizing: border-box;

  .img {
    width: 200px;
    margin-bottom: 40px;
  }

  .title {
    margin: 0;
    text-align: center;
    color: #fff;
    font-weight: bold;
    letter-spacing: 2px;
    font-size: 28px;
  }
}

.logout-border {
  position: relative;
  display: flex;
  align-items: center;
  width: 50%;
  box-sizing: border-box;
}

.logout-main {
  margin: 0 auto;
  padding: 50px;
  width: 70%;
  text-align: center;
  box-sizing: border-box;
}

.logout-icon {
  margin-bottom: 30px;
  animation-delay: 0.5s;
}

.logout-title {
  color: #2c77f1;
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 20px 0;
  letter-spacing: 2px;
}

.logout-message {
  color: #666;
  font-size: 16px;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.user-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 30px 0;
  border-left: 4px solid #2c77f1;

  .user-name {
    color: #2c77f1;
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 10px 0;
  }

  .logout-time-info {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.logout-actions {
  margin: 40px 0;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .relogin-btn {
    width: 100%;
    height: 50px;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 1px;
    background-color: #2c77f1;
    border-color: #2c77f1;
    transition: all 0.3s ease;

    &:hover {
      background-color: #1e5ce6;
      border-color: #1e5ce6;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(44, 119, 241, 0.3);
    }
  }

  .home-btn {
    width: 100%;
    height: 50px;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 1px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

.security-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
  margin-top: 30px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

// 响应式设计
@media (max-width: 768px) {
  .logout-wrapper {
    flex-direction: column;
  }

  .logout-left,
  .logout-border {
    width: 100%;
  }

  .logout-left {
    padding: 30px 20px;
    min-height: 200px;

    .img {
      width: 120px;
    }

    .title {
      font-size: 20px;
    }
  }

  .logout-main {
    padding: 30px 20px;
    width: 90%;
  }

  .logout-title {
    font-size: 24px;
  }

  .logout-actions {
    .relogin-btn,
    .home-btn {
      height: 45px;
      font-size: 14px;
    }
  }
}

// 工具类
.mr-2 {
  margin-right: 8px;
}
</style>
