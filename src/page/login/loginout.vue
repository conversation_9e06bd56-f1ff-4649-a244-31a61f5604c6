<template>
  <div class="education-logout-container">
    <!-- 顶部系统信息栏 -->
    <div class="header-bar">
      <div class="system-info">
        <h1 class="system-title">学情预警系统</h1>
        <span class="system-subtitle">Educational Early Warning System</span>
      </div>
      <div class="current-time">
        <el-icon><Clock /></el-icon>
        <span>{{ currentTime }}</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="logout-card animate__animated animate__fadeInUp">
        <!-- 教育主题图标区域 -->
        <div class="education-icon-section">
          <div class="icon-wrapper animate__animated animate__bounceIn">
            <el-icon :size="120" color="#1890ff">
              <Reading />
            </el-icon>
          </div>
          <div class="success-badge animate__animated animate__zoomIn">
            <el-icon :size="24" color="#52c41a">
              <CircleCheckFilled />
            </el-icon>
          </div>
        </div>

        <!-- 退出状态信息 -->
        <div class="logout-status">
          <h2 class="status-title">退出登录成功</h2>
          <p class="status-message">您已安全退出学情预警系统，感谢您的使用</p>
        </div>

        <!-- 用户信息卡片 -->
        <div class="user-card" v-if="userInfo">
          <div class="user-avatar">
            <el-icon :size="32" color="#1890ff">
              <User />
            </el-icon>
          </div>
          <div class="user-details">
            <p class="user-name">{{ getUserDisplayName() }}</p>
            <p class="logout-time">退出时间：{{ logoutTime }}</p>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <el-button type="primary" size="large" class="primary-action-btn" @click="handleReLogin" :loading="loading">
            <el-icon class="btn-icon"><User /></el-icon>
            重新登录
          </el-button>

          <el-button size="large" class="secondary-action-btn" @click="handleGoHome">
            <el-icon class="btn-icon"><House /></el-icon>
            返回首页
          </el-button>
        </div>

        <!-- 安全提示信息 -->
        <div class="security-notice">
          <el-icon color="#fa8c16"><WarningFilled /></el-icon>
          <span>为保护您的账户安全，请及时关闭浏览器或清除缓存</span>
        </div>
      </div>
    </div>

    <!-- 底部信息栏 -->
    <div class="footer-bar">
      <div class="system-version">
        <span>学情预警系统 v2.0</span>
        <span class="separator">|</span>
        <span>技术支持：教育信息化中心</span>
      </div>
      <div class="copyright">
        <span>&copy; 2024 教育管理平台 版权所有</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { Clock, Reading, CircleCheckFilled, User, House, WarningFilled } from "@element-plus/icons-vue";
import { getStore } from "@/utils/store";
import dayjs from "dayjs";

// 定义组件选项
defineOptions({
  name: "LoginLoginout",
});

// 组合式API实例
const router = useRouter();
const store = useStore();

// 响应式数据定义
const currentTime = ref("");
const logoutTime = ref("");
const loading = ref(false);
const userInfo = ref(null);
let timeInterval = null;

// 生命周期钩子
onMounted(() => {
  initializeLogoutPage();
  startTimeUpdater();
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

// 页面初始化方法
const initializeLogoutPage = () => {
  // 尝试获取用户信息（退出前的信息）
  userInfo.value = getStore({ name: "userInfo" }) || null;

  // 记录退出时间
  logoutTime.value = dayjs().format("YYYY年MM月DD日 HH:mm:ss");

  // 确保用户状态已完全清除
  ensureUserLoggedOut();

  // 添加页面进入动画延迟
  setTimeout(() => {
    document.querySelector(".logout-card")?.classList.add("animate__animated", "animate__fadeInUp");
  }, 100);
};

// 时间更新器
const startTimeUpdater = () => {
  const updateCurrentTime = () => {
    currentTime.value = dayjs().format("YYYY年MM月DD日 dddd HH:mm:ss");
  };

  updateCurrentTime();
  timeInterval = setInterval(updateCurrentTime, 1000);
};

// 确保用户已退出登录
const ensureUserLoggedOut = () => {
  if (store.getters.token) {
    store.dispatch("FedLogOut").catch((error) => {
      console.warn("清除用户登录状态时出现错误:", error);
    });
  }
};

// 获取用户显示名称
const getUserDisplayName = () => {
  if (!userInfo.value) return "用户";

  return userInfo.value.user_name || userInfo.value.name || userInfo.value.account || userInfo.value.username || "用户";
};

// 重新登录处理
const handleReLogin = async () => {
  loading.value = true;

  try {
    // 添加用户体验延迟
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 跳转到登录页面
    await router.push("/login");

    ElMessage.success("正在跳转到登录页面...");
  } catch (error) {
    console.error("跳转登录页面失败:", error);
    ElMessage.error("页面跳转失败，请手动刷新页面");
  } finally {
    loading.value = false;
  }
};

// 返回首页处理
const handleGoHome = () => {
  try {
    // 可以跳转到系统首页或公开页面
    window.location.href = "/";
  } catch (error) {
    console.error("跳转首页失败:", error);
    ElMessage.error("页面跳转失败");
  }
};
</script>

<style lang="scss" scoped>
// 学情预警系统退出页面样式

.education-logout-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  position: relative;
  overflow: hidden;

  // 背景装饰元素
  &::before {
    content: "";
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  &::after {
    content: "";
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite reverse;
  }
}

// 顶部系统信息栏
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 10;
  position: relative;

  .system-info {
    .system-title {
      color: #fff;
      font-size: 24px;
      font-weight: bold;
      margin: 0 0 5px 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .system-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      font-style: italic;
    }
  }

  .current-time {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;

    .el-icon {
      font-size: 18px;
    }
  }
}

// 主要内容区域
.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  position: relative;
  z-index: 5;
}

// 退出登录卡片
.logout-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 60px 50px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 500px;
  width: 100%;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.3);

  // 卡片装饰边框
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1890ff, #52c41a, #fa8c16);
    border-radius: 24px 24px 0 0;
  }
}

// 教育主题图标区域
.education-icon-section {
  position: relative;
  margin-bottom: 40px;

  .icon-wrapper {
    position: relative;
    display: inline-block;
    padding: 20px;
    background: linear-gradient(135deg, #e6f7ff, #f6ffed);
    border-radius: 50%;
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
  }

  .success-badge {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: #fff;
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
    animation-delay: 1s;
  }
}

// 退出状态信息
.logout-status {
  margin-bottom: 40px;

  .status-title {
    color: #1890ff;
    font-size: 32px;
    font-weight: bold;
    margin: 0 0 16px 0;
    text-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
  }

  .status-message {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
  }
}

// 用户信息卡片
.user-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f0f9ff, #f6ffed);
  border-radius: 16px;
  padding: 24px;
  margin: 30px 0;
  border-left: 4px solid #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);

  .user-avatar {
    background: #fff;
    border-radius: 50%;
    padding: 12px;
    margin-right: 20px;
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
  }

  .user-details {
    flex: 1;
    text-align: left;

    .user-name {
      color: #1890ff;
      font-size: 18px;
      font-weight: bold;
      margin: 0 0 8px 0;
    }

    .logout-time {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }
}

// 操作按钮区域
.action-buttons {
  margin: 40px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .primary-action-btn {
    height: 56px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 12px;
    background: linear-gradient(135deg, #1890ff, #096dd9);
    border: none;
    box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(24, 144, 255, 0.4);
      background: linear-gradient(135deg, #096dd9, #0050b3);
    }

    &:active {
      transform: translateY(0);
    }

    .btn-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .secondary-action-btn {
    height: 56px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 12px;
    background: #fff;
    border: 2px solid #d9d9d9;
    color: #666;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      border-color: #1890ff;
      color: #1890ff;
      box-shadow: 0 6px 20px rgba(24, 144, 255, 0.2);
    }

    &:active {
      transform: translateY(0);
    }

    .btn-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

// 安全提示信息
.security-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, #fff7e6, #fff2e8);
  border: 1px solid #ffd591;
  border-radius: 12px;
  padding: 16px 20px;
  margin-top: 30px;
  color: #d46b08;
  font-size: 14px;
  line-height: 1.5;

  .el-icon {
    font-size: 16px;
    flex-shrink: 0;
  }
}

// 底部信息栏
.footer-bar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  position: relative;
  z-index: 10;

  .system-version {
    display: flex;
    align-items: center;
    gap: 12px;

    .separator {
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .copyright {
    font-style: italic;
  }
}

// 动画定义
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header-bar {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
    text-align: center;

    .system-info .system-title {
      font-size: 20px;
    }

    .current-time {
      font-size: 14px;
    }
  }

  .main-content {
    padding: 20px 15px;
  }

  .logout-card {
    padding: 40px 30px;
    border-radius: 20px;

    .education-icon-section {
      margin-bottom: 30px;

      .icon-wrapper .el-icon {
        font-size: 80px !important;
      }
    }

    .logout-status .status-title {
      font-size: 24px;
    }

    .user-card {
      flex-direction: column;
      text-align: center;
      gap: 15px;

      .user-avatar {
        margin-right: 0;
      }

      .user-details {
        text-align: center;
      }
    }

    .action-buttons {
      .primary-action-btn,
      .secondary-action-btn {
        height: 50px;
        font-size: 15px;
      }
    }
  }

  .footer-bar {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
    text-align: center;

    .system-version {
      flex-direction: column;
      gap: 5px;

      .separator {
        display: none;
      }
    }
  }
}

@media (max-width: 480px) {
  .logout-card {
    padding: 30px 20px;
    margin: 0 10px;

    .education-icon-section .icon-wrapper .el-icon {
      font-size: 60px !important;
    }

    .logout-status .status-title {
      font-size: 20px;
    }

    .action-buttons {
      .primary-action-btn,
      .secondary-action-btn {
        height: 48px;
        font-size: 14px;
      }
    }
  }
}

// 深色模式适配（可选）
@media (prefers-color-scheme: dark) {
  .education-logout-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .logout-card {
    background: rgba(44, 62, 80, 0.95);
    color: #ecf0f1;

    .logout-status .status-message {
      color: #bdc3c7;
    }

    .user-card {
      background: linear-gradient(135deg, #34495e, #2c3e50);

      .user-details .logout-time {
        color: #bdc3c7;
      }
    }

    .secondary-action-btn {
      background: #34495e;
      border-color: #7f8c8d;
      color: #ecf0f1;

      &:hover {
        border-color: #3498db;
        color: #3498db;
      }
    }
  }
}
</style>
